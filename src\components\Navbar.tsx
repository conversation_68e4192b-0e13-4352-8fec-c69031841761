import { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
// Removed asset imports - using direct paths to public/images

interface NavbarProps {
  language: 'vi' | 'en';
  toggleLanguage: () => void;
}

const Navbar = ({ language, toggleLanguage }: NavbarProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
  const navigate = useNavigate();

  const dropdownRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLElement>(null);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Calculate header height and update body padding
  useEffect(() => {
    const updateHeaderHeight = () => {
      if (headerRef.current) {
        const headerHeight = headerRef.current.offsetHeight;
        document.body.style.paddingTop = `${headerHeight}px`;
      }
    };

    // Initial calculation
    updateHeaderHeight();

    // Recalculate on window resize
    window.addEventListener('resize', updateHeaderHeight);
    return () => {
      window.removeEventListener('resize', updateHeaderHeight);
    };
  }, []);

  const navItems = [
    {
      en: 'HOME',
      vi: 'TRANG CHỦ',
      path: '/',
      dropdown: false
    },
    {
      en: 'ABOUT US',
      vi: 'GIỚI THIỆU',
      path: '/about',
      dropdown: true,
      submenu: [
        {
          en: 'Our Passion',
          vi: 'Đam Mê Của Chúng Tôi',
          path: '/about/passion'
        },
        {
          en: 'Sustainability',
          vi: 'Phát Triển Bền Vững',
          path: '/about/sustainability'
        }
      ]
    },
    {
      en: 'PRODUCTS',
      vi: 'SẢN PHẨM',
      path: '/products',
      dropdown: true,
      megaMenu: true,
      categories: [
        {
          en: 'FRESH FRUIT',
          vi: 'TRÁI CÂY TƯƠI',
          products: [
            { en: 'AVOCADO', vi: 'BƠ', path: '/products/fresh-fruit/avocado' },
            { en: 'BANANA', vi: 'CHUỐI', path: '/products/fresh-fruit/banana' },
            { en: 'PITAYA', vi: 'THANH LONG', path: '/products/fresh-fruit/pitaya' },
            { en: 'DURIAN', vi: 'SẦU RIÊNG', path: '/products/fresh-fruit/durian' },
            { en: 'JACKFRUIT', vi: 'MÍT', path: '/products/fresh-fruit/jackfruit' },
            { en: 'MANGO', vi: 'XOÀI', path: '/products/fresh-fruit/mango' },
            { en: 'COCONUT', vi: 'DỪA', path: '/products/fresh-fruit/coconut' },
            { en: 'PINEAPPLE', vi: 'KHÓM', path: '/products/fresh-fruit/pineapple' }
          ]
        },
        {
          en: 'FROZEN FRUIT',
          vi: 'TRÁI CÂY ĐÔNG LẠNH',
          products: [
            { en: 'FROZEN AVOCADO', vi: 'BƠ ĐÔNG LẠNH', path: '/products/frozen-fruit/frozen-avocado' },
            { en: 'FROZEN BANANA', vi: 'CHUỐI ĐÔNG LẠNH', path: '/products/frozen-fruit/frozen-banana' },
            { en: 'FROZEN PITAYA', vi: 'THANH LONG ĐÔNG LẠNH', path: '/products/frozen-fruit/frozen-pitaya' },
            { en: 'FROZEN DURIAN', vi: 'SẦU RIÊNG ĐÔNG LẠNH', path: '/products/frozen-fruit/frozen-durian' },
            { en: 'FROZEN JACKFRUIT', vi: 'MÍT ĐÔNG LẠNH', path: '/products/frozen-fruit/frozen-jackfruit' },
            { en: 'FROZEN MANGO', vi: 'XOÀI ĐÔNG LẠNH', path: '/products/frozen-fruit/frozen-mango' },
            { en: 'FROZEN COCONUT MEAT', vi: 'CƠM DỪA ĐÔNG LẠNH', path: '/products/frozen-fruit/frozen-coconut-meat' },
            { en: 'FROZEN PINEAPPLE', vi: 'KHÓM ĐÔNG LẠNH', path: '/products/frozen-fruit/frozen-pineapple' }
          ]
        },
        {
          en: 'DRIED FRUIT',
          vi: 'TRÁI CÂY SẤY KHÔ',
          products: [
            { en: 'DRIED BANANA', vi: 'CHUỐI SẤY KHÔ', path: '/products/dried-fruit/dried-banana' },
            { en: 'DRIED JACKFRUIT', vi: 'MÍT SẤY KHÔ', path: '/products/dried-fruit/dried-jackfruit' },
            { en: 'DRIED MANGO', vi: 'XOÀI SẤY KHÔ', path: '/products/dried-fruit/dried-mango' },
            { en: 'DRIED CASHEW NUT', vi: 'HẠT ĐIỀU SẤY KHÔ', path: '/products/dried-fruit/dried-cashew-nut' },
            { en: 'DRIED CARROTS', vi: 'CÀ RỐT SẤY KHÔ', path: '/products/dried-fruit/dried-carrots' },
            { en: 'DRIED LOTUS SEED', vi: 'HẠT SEN SẤY KHÔ', path: '/products/dried-fruit/dried-lotus-seed' },
            { en: 'DRIED COCONUT', vi: 'DỪA SẤY KHÔ', path: '/products/dried-fruit/dried-coconut' },
            { en: 'DRIED SWEET POTATO', vi: 'KHOAI LANG SẤY KHÔ', path: '/products/dried-fruit/dried-sweet-potato' }
          ]
        },
        {
          en: 'FREEZE DRIED FRUIT',
          vi: 'TRÁI CÂY ĐÔNG KHÔ',
          products: [
            { en: 'FD AVOCADO', vi: 'BƠ ĐÔNG KHÔ', path: '/products/freeze-dried-fruit/fd-avocado' },
            { en: 'FD BANANA', vi: 'CHUỐI ĐÔNG KHÔ', path: '/products/freeze-dried-fruit/fd-banana' },
            { en: 'FD PITAYA', vi: 'THANH LONG ĐÔNG KHÔ', path: '/products/freeze-dried-fruit/fd-pitaya' },
            { en: 'FD DURIAN', vi: 'SẦU RIÊNG ĐÔNG KHÔ', path: '/products/freeze-dried-fruit/fd-durian' },
            { en: 'FD JACKFRUIT', vi: 'MÍT ĐÔNG KHÔ', path: '/products/freeze-dried-fruit/fd-jackfruit' },
            { en: 'FD MANGO', vi: 'XOÀI ĐÔNG KHÔ', path: '/products/freeze-dried-fruit/fd-mango' },
            { en: 'FD RAMBUTAN', vi: 'CHÔM CHÔM ĐÔNG KHÔ', path: '/products/freeze-dried-fruit/fd-rambutan' },
            { en: 'FD PINEAPPLE', vi: 'KHÓM ĐÔNG KHÔ', path: '/products/freeze-dried-fruit/fd-pineapple' }
          ]
        }
      ]
    },
    {
      en: 'CONTACT US',
      vi: 'LIÊN HỆ',
      path: '/contact',
      dropdown: false
    }
  ];

  return (
    <header ref={headerRef} className="fixed-header">
      {/* Top bar */}
      <div className="bg-[rgb(19,104,174)] text-white py-2 w-full">
        <div className="w-full flex justify-between items-center px-1">
          {/* Left side - Logo area alignment */}
          <div className="md:w-1/4 w-64 flex ml-[60%]">
            {/* Empty space to align with logo below */}
          </div>

          {/* Center - Menu area alignment */}
          <div className="md:w-2/4 hidden md:flex justify-center">
            <div className="flex space-x-4 items-center text-xs">
              <Link
                to="/register"
                className="text-white hover:text-gray-200 transition-colors duration-200 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
                {language === 'vi' ? 'Tạo tài khoản' : 'Create an Account'}
              </Link>
              <span className="text-white">|</span>
              <Link
                to="/login"
                className="text-white hover:text-gray-200 transition-colors duration-200 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                {language === 'vi' ? 'Đăng nhập' : 'Sign In'}
              </Link>
            </div>
          </div>

          {/* Right side - Language area alignment */}
          <div className="md:w-1/4 mr-16">
            <div className="flex items-center justify-end">
              <button
                className="text-white hover:text-gray-200 transition-colors duration-200 flex items-center mr-2"
                onClick={() => {/* Implement search functionality */}}
                aria-label="Search"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <span>{language === 'vi' ? 'Tìm kiếm' : 'search'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      {/* <div className="bg-[rgb(19,104,174)] text-white py-2 w-full">
        <div className="flex justify-between w-full px-1">
          <div className="border border-red-200 rounded-md p-2 w-64 flex justify-start ml-[2%]">
            <div style={{ textAlign: 'left' }}>AN BINH FOODS CO, LTD</div>
          </div>

          <div className="flex items-center border border-red-200 rounded-md p-2 mr-[2%]">
            <div className="relative group mr-4">
              <button
                className="text-white hover:text-gray-200 flex items-center"
              >
                <img
                  src={language === 'vi' ? flagVN : flagEN}
                  alt={language === 'vi' ? 'Cờ Việt Nam' : 'UK Flag'}
                  className="w-6 h-4 mr-2 inline-block rounded-sm border border-gray-300 shadow-sm"
                />
                <span>{language === 'vi' ? 'Tiếng Việt' : 'English'}</span>
                <svg
                  className="ml-1 h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute right-0 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-20 hidden group-hover:block overflow-hidden">
                <button
                  onClick={language === 'vi' ? undefined : toggleLanguage}
                  className={`block w-full text-left px-4 py-3 text-sm ${language === 'vi' ? 'bg-gray-100 text-gray-600 font-medium cursor-default' : 'text-gray-800 hover:bg-gray-100'} flex items-center`}
                  disabled={language === 'vi'}
                >
                  <img src={flagVN} alt="Cờ Việt Nam" className="w-6 h-4 mr-2 inline-block rounded-sm border border-gray-300 shadow-sm" />
                  Tiếng Việt
                </button>
                <button
                  onClick={language === 'en' ? undefined : toggleLanguage}
                  className={`block w-full text-left px-4 py-3 text-sm ${language === 'en' ? 'bg-gray-100 text-gray-600 font-medium cursor-default' : 'text-gray-800 hover:bg-gray-100'} flex items-center`}
                  disabled={language === 'en'}
                >
                  <img src={flagEN} alt="UK Flag" className="w-6 h-4 mr-2 inline-block rounded-sm border border-gray-300 shadow-sm" />
                  English
                </button>
              </div>
            </div>
            <div className="flex space-x-2">
              <a href="#" className="text-white hover:text-gray-200">
                <span className="sr-only">Facebook</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                </svg>
              </a>
              <a href="#" className="text-white hover:text-gray-200">
                <span className="sr-only">WhatsApp</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z" />
                </svg>
              </a>
              <a href="#" className="text-white hover:text-gray-200">
                <span className="sr-only">YouTube</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div> */}

      {/* Main navbar */}
      <nav className="bg-white shadow-md border-t-2 ">
        <div className="w-full px-1">
          <div className="flex justify-between items-center py-2">
            <div className="md:w-1/4 w-64 flex  ml-[2%]">
              <Link to="/" className="flex items-center justify-center">
                <img
                  src="/images/assets/logo.png"
                  alt="AN BINH FOODS Logo"
                  className="h-12 w-auto ml-24"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/images/assets/logo-placeholder.svg";
                  }}
                />
                {/* <div className="ml-3 flex sflex-col justify-start">
                  <span className="text-green-800 italic text-lg font-medium">Healthy life with natural food</span>
                </div> */}
              </Link>
            </div>

            {/* Desktop menu */}

            <div className="md:w-2/4 hidden md:flex items-center justify-center " ref={dropdownRef}>
              {navItems.map((item, index) => (
                <div key={index} className="relative group mx-6">
                  {item.dropdown ? (
                    <div
                      className="text-gray-800 hover:text-green-600 font-bold cursor-pointer flex items-center font-serif text-sm "
                      onMouseEnter={() => setActiveDropdown(index)}
                    >
                      {language === 'vi' ? item.vi : item.en}
                      <svg
                        className="ml-1 h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      className="text-gray-800 hover:text-green-600 font-bold  cursor-pointer flex items-center font-serif  text-sm "
                    >
                      {language === 'vi' ? item.vi : item.en}
                    </Link>
                  )}

                  {/* Dropdown menu */}
                  {item.dropdown && activeDropdown === index && !item.megaMenu && (
                    <div
                      className="absolute left-1/2 transform -translate-x-1/2 mt-2 w-48 bg-white border border-gray-200 rounded-xl shadow-xl py-3 z-10 transition-all duration-300 ease-in-out text-center"
                      onMouseLeave={() => setActiveDropdown(null)}
                    >
                      {item.submenu?.map((subItem, subIndex) => (
                        <Link
                          key={subIndex}
                          to={subItem.path}
                          className="block px-4 py-2 text-sm text-black hover:bg-green-50 hover:text-green-600 transition-colors duration-200 whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                        >
                          {language === 'vi' ? subItem.vi : subItem.en}
                        </Link>
                      ))}
                    </div>
                  )}

                  {/* Mega Menu for Products */}
                  {item.dropdown && activeDropdown === index && item.megaMenu && (
                    <div
                      className="absolute left-1/2 transform -translate-x-1/2 mt-2 w-[1000px] bg-white border border-gray-200 rounded-xl shadow-xl py-6 px-2 z-10 grid grid-cols-4 gap-4 transition-all duration-300 ease-in-out"
                      onMouseLeave={() => setActiveDropdown(null)}
                    >
                      {item.categories?.map((category, catIndex) => (
                        <div key={catIndex} className="px-4 min-w-[200px]">
                          <h3 className="font-semibold  text-black mb-3 border-b border-gray-200 pb-2 text-left
                           whitespace-nowrap overflow-hidden text-overflow-ellipsis text-sm md:text-base uppercase">
                            {language === 'vi' ? category.vi : category.en}
                          </h3>
                          <ul className="space-y-1">
                            {category.products.map((product, prodIndex) => (
                              <li key={prodIndex}>
                                <Link
                                  to={product.path}
                                  className="block text-xs font-semibold font-sans  text-gray-700 hover:text-green-600 py-1 transition-colors duration-200 whitespace-nowrap overflow-hidden text-overflow-ellipsis text-left uppercase tracking-wide"
                                >
                                  {language === 'vi' ? product.vi : product.en}
                                </Link>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
            <div className='md:w-1/4  mr-[2%]'>
            <div className="flex items-center justify-end">
            <div className="relative group mr-2">
              <button
                className="text-blue hover:text-gray-200 flex items-center"
              >
                <img
                  src={language === 'vi' ? '/images/assets/flag-vn.svg' : '/images/assets/flag-en.svg'}
                  alt={language === 'vi' ? 'Cờ Việt Nam' : 'UK Flag'}
                  className="w-6 h-4 mr-2 inline-block rounded-sm border border-gray-300 shadow-sm"
                />
                <span>{language === 'vi' ? 'Vietnam' : 'English'}</span>
                <svg
                  className="ml-1 h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute right-0 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-20 hidden group-hover:block overflow-hidden">
                <button
                  onClick={language === 'vi' ? undefined : toggleLanguage}
                  className={`block w-full text-left px-4 py-3 text-sm ${language === 'vi' ? 'bg-gray-100 text-gray-600 font-medium cursor-default' : 'text-gray-800 hover:bg-gray-100'} flex items-center`}
                  disabled={language === 'vi'}
                >
                  <img src="/images/assets/flag-vn.svg" alt="Cờ Việt Nam" className="w-6 h-4 mr-2 inline-block rounded-sm border border-gray-300 shadow-sm" />
                  Vietnam
                </button>
                <button
                  onClick={language === 'en' ? undefined : toggleLanguage}
                  className={`block w-full text-left px-4 py-3 text-sm ${language === 'en' ? 'bg-gray-100 text-gray-600 font-medium cursor-default' : 'text-gray-800 hover:bg-gray-100'} flex items-center`}
                  disabled={language === 'en'}
                >
                  <img src="/images/assets/flag-en.svg" alt="UK Flag" className="w-6 h-4 mr-2 inline-block rounded-sm border border-gray-300 shadow-sm" />
                  English
                </button>
              </div>
            </div>
            {/* <div className="flex space-x-7">
              <a href="#" className="text-[rgb(19,104,174)]  hover:text-gray-200">
                <span className="sr-only">Facebook</span>
                <svg className="h-7 w-7" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                </svg>
              </a>
              <a href="#" className="text-[rgb(51,188,32)] hover:text-gray-200">
                <span className="sr-only">WhatsApp</span>
                <svg className="h-7 w-7" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.031 6.172c-3.181 0-5.767 2.586-5.768 5.766-.001 1.298.38 2.27 1.019 3.287l-.582 2.128 2.182-.573c.978.58 1.911.928 3.145.929 3.178 0 5.767-2.587 5.768-5.766.001-3.187-2.575-5.77-5.764-5.771zm3.392 8.244c-.144.405-.837.774-1.17.824-.299.045-.677.063-1.092-.069-.252-.08-.575-.187-.988-.365-1.739-.751-2.874-2.502-2.961-2.617-.087-.116-.708-.94-.708-1.793s.448-1.273.607-1.446c.159-.173.346-.217.462-.217l.332.006c.106.005.249-.04.39.298.144.347.491 1.2.534 1.287.043.087.072.188.014.304-.058.116-.087.188-.173.289l-.26.304c-.087.086-.177.18-.076.354.101.174.449.741.964 1.201.662.591 1.221.774 1.394.86s.274.072.376-.043c.101-.116.433-.506.549-.68.116-.173.231-.145.39-.087s1.011.477 1.184.564.289.13.332.202c.045.72.045.419-.1.824zm-3.423-14.416c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm.029 18.88c-1.161 0-2.305-.292-3.318-.844l-3.677.964.984-3.595c-.607-1.052-.927-2.246-.926-3.468.001-3.825 3.113-6.937 6.937-6.937 1.856.001 3.598.723 4.907 2.034 1.31 1.311 2.031 3.054 2.03 4.908-.001 3.825-3.113 6.938-6.937 6.938z"/>
                </svg>
              </a>
              <a href="#" className="text-[rgb(175,24,26)] hover:text-gray-200">
                <span className="sr-only">YouTube</span>
                <svg className="h-7 w-7" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
                </svg>
              </a>
            </div> */}
          </div>
            {/* Mobile menu button */}
            <div className="md:hidden flex items-center">
              <button
                onClick={toggleMenu}
                className="text-gray-800 hover:text-red-600 focus:outline-none"
              >
                {isOpen ? (
                  <XMarkIcon className="h-6 w-6" />
                ) : (
                  <Bars3Icon className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
          </div>

          {/* Mobile menu */}
          {isOpen && (
            <div className="md:hidden py-4 border-t">
              <div className="flex flex-col space-y-4">
                {navItems.map((item, index) => (
                  <div key={index}>
                    {item.dropdown ? (
                      <>
                        <div className="flex items-center">
                          <Link
                            to={item.path}
                            className="text-gray-800 hover:text-red-600 font-medium px-4 py-2 flex-1"
                            onClick={() => setIsOpen(false)}
                          >
                            {language === 'vi' ? item.vi : item.en}
                          </Link>
                          <button
                            className="text-gray-800 hover:text-red-600 px-4 py-2"
                            onClick={() => setActiveDropdown(activeDropdown === index ? null : index)}
                          >
                            <svg
                              className={`h-4 w-4 transition-transform ${activeDropdown === index ? 'transform rotate-180' : ''}`}
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </button>
                        </div>
                        {activeDropdown === index && !item.megaMenu && (
                          <div
                            className="pl-8 py-2 space-y-2 bg-red-100 rounded-lg mx-4 mt-2 border-2 border-red-500 relative z-50"
                            style={{ pointerEvents: 'auto' }}
                            onClick={(e) => {
                              console.log('Dropdown container clicked');
                              e.stopPropagation();
                            }}
                            onTouchStart={() => console.log('Touch start on container')}
                          >
                            {item.submenu?.map((subItem, subIndex) => (
                              <div
                                key={subIndex}
                                className="bg-blue-100 border border-blue-300 p-1 relative z-50"
                                style={{ pointerEvents: 'auto' }}
                                onClick={(e) => {
                                  console.log('Submenu item wrapper clicked:', subItem.path);
                                  e.stopPropagation();
                                  setIsOpen(false);
                                  setActiveDropdown(null);
                                  navigate(subItem.path);
                                }}
                                onTouchStart={() => console.log('Touch start on wrapper:', subItem.path)}
                              >
                                <button
                                  className="block w-full text-left text-gray-700 hover:text-green-600 py-2 px-2 transition-colors duration-200 whitespace-nowrap overflow-hidden text-overflow-ellipsis cursor-pointer relative z-50 bg-yellow-100 border border-yellow-300"
                                  style={{ pointerEvents: 'auto' }}
                                  onClick={(e) => {
                                    console.log('Submenu button clicked:', subItem.path);
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setIsOpen(false);
                                    setActiveDropdown(null);
                                    navigate(subItem.path);
                                  }}
                                  onTouchStart={() => console.log('Touch start on button:', subItem.path)}
                                  onMouseDown={() => console.log('Mouse down on button:', subItem.path)}
                                >
                                  {language === 'vi' ? subItem.vi : subItem.en}
                                </button>
                              </div>
                            ))}
                          </div>
                        )}

                        {activeDropdown === index && item.megaMenu && (
                          <div
                            className="py-2 px-4 bg-gray-50 rounded-lg mx-2 mt-2 border border-gray-200"
                            onClick={(e) => e.stopPropagation()}
                          >
                            {item.categories?.map((category, catIndex) => (
                              <div key={catIndex} className="mb-6">
                                <h3 className="font-bold text-gray-800 mb-3 border-b border-gray-200 pb-2 text-left whitespace-nowrap overflow-hidden text-overflow-ellipsis text-sm md:text-base uppercase">
                                  {language === 'vi' ? category.vi : category.en}
                                </h3>
                                <ul className="pl-4 space-y-2">
                                  {category.products.map((product, prodIndex) => (
                                    <li key={prodIndex}>
                                      <button
                                        className="block w-full text-left text-sm text-gray-500 hover:text-green-600 py-1.5 transition-colors duration-200 whitespace-nowrap overflow-hidden text-overflow-ellipsis uppercase tracking-wide cursor-pointer relative z-10 bg-transparent border-none"
                                        onClick={(e) => {
                                          console.log('Product clicked:', product.path);
                                          e.preventDefault();
                                          e.stopPropagation();
                                          setIsOpen(false);
                                          setActiveDropdown(null);
                                          navigate(product.path);
                                        }}
                                      >
                                        {language === 'vi' ? product.vi : product.en}
                                      </button>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            ))}
                          </div>
                        )}
                      </>
                    ) : (
                      <Link
                        to={item.path}
                        className="text-gray-800 hover:text-red-600 font-medium px-4 py-2 block"
                        onClick={() => setIsOpen(false)}
                      >
                        {language === 'vi' ? item.vi : item.en}
                      </Link>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </nav>
    </header>
  );
};

export default Navbar;
